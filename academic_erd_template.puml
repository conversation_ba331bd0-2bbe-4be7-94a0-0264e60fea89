@startuml Academic_Style_ERD
!theme plain
skinparam backgroundColor white
skinparam entity {
    BackgroundColor white
    BorderColor black
    FontName Arial
    FontSize 10
}
skinparam class {
    BackgroundColor white
    BorderColor black
    FontName Arial
    FontSize 10
}

' 学术论文风格的实体关系图模板
' 主键用下划线标识，外键用斜体标识

entity "用户 (User)" as User {
    * <u>user_id</u> : INTEGER <<PK>>
    --
    username : VARCHA<PERSON>(50)
    email : VARCHAR(100)
    password_hash : VARCHAR(255)
    phone : VARCHAR(20)
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

entity "商品 (Product)" as Product {
    * <u>product_id</u> : INTEGER <<PK>>
    --
    product_name : VARCHAR(100)
    description : TEXT
    price : DECIMAL(10,2)
    stock_quantity : INTEGER
    category_id : INTEGER <<FK>>
    created_at : TIMESTAMP
    updated_at : TIMESTAMP
}

entity "商品分类 (Category)" as Category {
    * <u>category_id</u> : INTEGER <<PK>>
    --
    category_name : VARCHAR(50)
    description : TEXT
    parent_category_id : INTEGER <<FK>>
}

entity "订单 (Order)" as Order {
    * <u>order_id</u> : INTEGER <<PK>>
    --
    user_id : INTEGER <<FK>>
    order_date : TIMESTAMP
    total_amount : DECIMAL(10,2)
    status : VARCHAR(20)
    shipping_address : TEXT
    payment_method : VARCHAR(50)
}

entity "订单明细 (OrderItem)" as OrderItem {
    * <u>order_item_id</u> : INTEGER <<PK>>
    --
    order_id : INTEGER <<FK>>
    product_id : INTEGER <<FK>>
    quantity : INTEGER
    unit_price : DECIMAL(10,2)
    subtotal : DECIMAL(10,2)
}

entity "购物车 (Cart)" as Cart {
    * <u>cart_id</u> : INTEGER <<PK>>
    --
    user_id : INTEGER <<FK>>
    product_id : INTEGER <<FK>>
    quantity : INTEGER
    added_at : TIMESTAMP
}

entity "商品评价 (Review)" as Review {
    * <u>review_id</u> : INTEGER <<PK>>
    --
    user_id : INTEGER <<FK>>
    product_id : INTEGER <<FK>>
    rating : INTEGER
    comment : TEXT
    review_date : TIMESTAMP
}

' 关系定义（包含基数标注）
User ||--o{ Order : "1:N\n一个用户可以有多个订单"
Order ||--o{ OrderItem : "1:N\n一个订单包含多个商品"
Product ||--o{ OrderItem : "1:N\n一个商品可以在多个订单中"
Product }o--|| Category : "N:1\n多个商品属于一个分类"
Category ||--o{ Category : "1:N\n分类可以有子分类"
User ||--o{ Cart : "1:N\n一个用户有一个购物车"
Product ||--o{ Cart : "1:N\n购物车包含多个商品"
User ||--o{ Review : "1:N\n一个用户可以写多个评价"
Product ||--o{ Review : "1:N\n一个商品可以有多个评价"

' 布局优化
!define DIRECTION top to bottom direction

note top of User : 系统用户实体\n存储用户基本信息
note top of Product : 商品实体\n存储商品详细信息
note top of Order : 订单实体\n记录用户购买行为

@enduml
