@startuml Academic_ERD
!theme plain
skinparam backgroundColor white
skinparam entity {
    BackgroundColor white
    BorderColor black
    FontName Arial
    FontSize 10
}

entity "学生 (Student)" as Student {
    * <u>student_id</u> : INTEGER <<PK>>
    --
    student_name : VARCHA<PERSON>(100)
    email : VARCHAR(100)
    phone : VARCHAR(20)
    <i>major_id</i> : INTEGER <<FK>>
    enrollment_date : DATE
    status : VARCHAR(20)
}

entity "专业 (Major)" as Major {
    * <u>major_id</u> : INTEGER <<PK>>
    --
    major_name : VARCHAR(100)
    <i>department_id</i> : INTEGER <<FK>>
    degree_type : VARCHAR(50)
    credit_hours : INTEGER
}

entity "院系 (Department)" as Department {
    * <u>department_id</u> : INTEGER <<PK>>
    --
    department_name : VARCHAR(100)
    head_name : VARCHAR(100)
    building : VARCHAR(50)
    phone : VARCHAR(20)
}

entity "课程 (Course)" as Course {
    * <u>course_id</u> : INTEGER <<PK>>
    --
    course_name : VARCHAR(100)
    course_code : VARCHAR(20)
    credits : INTEGER
    <i>department_id</i> : INTEGER <<FK>>
    description : TEXT
}

entity "选课记录 (Enrollment)" as Enrollment {
    * <u>enrollment_id</u> : INTEGER <<PK>>
    --
    <i>student_id</i> : INTEGER <<FK>>
    <i>course_id</i> : INTEGER <<FK>>
    semester : VARCHAR(20)
    year : INTEGER
    grade : DECIMAL(3,2)
    enrollment_date : DATE
}

entity "教师 (Instructor)" as Instructor {
    * <u>instructor_id</u> : INTEGER <<PK>>
    --
    instructor_name : VARCHAR(100)
    email : VARCHAR(100)
    <i>department_id</i> : INTEGER <<FK>>
    title : VARCHAR(50)
    hire_date : DATE
}

entity "授课记录 (Teaching)" as Teaching {
    * <u>teaching_id</u> : INTEGER <<PK>>
    --
    <i>instructor_id</i> : INTEGER <<FK>>
    <i>course_id</i> : INTEGER <<FK>>
    semester : VARCHAR(20)
    year : INTEGER
    classroom : VARCHAR(50)
}

' 关系定义
Student }o--|| Major : "N:1\n多个学生属于一个专业"
Major }o--|| Department : "N:1\n多个专业属于一个院系"
Course }o--|| Department : "N:1\n多个课程属于一个院系"
Student ||--o{ Enrollment : "1:N\n一个学生可以选多门课程"
Course ||--o{ Enrollment : "1:N\n一门课程可以被多个学生选择"
Instructor }o--|| Department : "N:1\n多个教师属于一个院系"
Instructor ||--o{ Teaching : "1:N\n一个教师可以教多门课程"
Course ||--o{ Teaching : "1:N\n一门课程可以由多个教师教授"

@enduml
