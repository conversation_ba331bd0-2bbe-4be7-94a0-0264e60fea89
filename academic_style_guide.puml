@startuml Academic_ERD_Style_Guide
!theme plain

' 学术论文标准ERD样式配置
skinparam backgroundColor white
skinparam entity {
    BackgroundColor white
    BorderColor black
    BorderThickness 1
    FontName "Times New Roman"
    FontSize 10
    AttributeFontSize 9
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
    FontSize 8
}

' 样式说明实体
entity "实体名称 (Entity)" as Entity1 {
    * <u>主键字段</u> : 数据类型 <<PK>>
    --
    普通字段 : 数据类型
    <i>外键字段</i> : 数据类型 <<FK>>
    可选字段 : 数据类型 {O}
    必填字段 : 数据类型 {M}
}

entity "关联实体 (Related)" as Entity2 {
    * <u>entity_id</u> : INTEGER <<PK>>
    --
    name : VARCHAR(100) {M}
    description : TEXT {O}
    <i>parent_id</i> : INTEGER <<FK>>
    created_at : TIMESTAMP
}

' 关系类型示例
Entity1 ||--|| Entity2 : "1:1 一对一关系"
Entity1 ||--o{ Entity2 : "1:N 一对多关系"  
Entity1 }o--o{ Entity2 : "M:N 多对多关系"
Entity1 }o--|| Entity2 : "N:1 多对一关系"

' 格式说明注释
note top of Entity1
  <b>学术ERD格式规范：</b>
  • 主键：<u>下划线</u> + <<PK>>
  • 外键：<i>斜体</i> + <<FK>>
  • 必填：{M} (Mandatory)
  • 可选：{O} (Optional)
  • 实体名：中英文对照
end note

note bottom of Entity2
  <b>关系基数标注：</b>
  • ||--|| : 一对一 (1:1)
  • ||--o{ : 一对多 (1:N)
  • }o--o{ : 多对多 (M:N)
  • }o--|| : 多对一 (N:1)
end note

' 图例
legend right
  <b>符号说明</b>
  ====
  <u>下划线</u> = 主键
  <i>斜体</i> = 外键
  {M} = 必填字段
  {O} = 可选字段
  <<PK>> = Primary Key
  <<FK>> = Foreign Key
endlegend

@enduml
